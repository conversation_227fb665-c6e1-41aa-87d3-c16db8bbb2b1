

from langchain.llms import OpenAI
from langchain.chains.question_answering import load_qa_chain

def test_langchain_llm_response():
    """Test of een eenvoudige LangChain QA-chain werkt."""
    try:
        llm = OpenAI(temperature=0.0)
        chain = load_qa_chain(llm, chain_type="stuff")
        docs = ["LangChain is een framework om applicaties met LLMs te bouwen."]
        question = "Wat is LangChain?"
        response = chain.run(input_documents=docs, question=question)
        assert "framework" in response.lower()
        print("✅ LangChain test succesvol:", response)
    except Exception as e:
        print("❌ LangChain test gefaald:", str(e))
