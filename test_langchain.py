

# Updated imports for modern LangChain
try:
    from langchain_openai import OpenAI
except ImportError:
    try:
        from langchain.llms import OpenAI
    except ImportError:
        print("⚠️ LangChain OpenAI not available")
        OpenAI = None

try:
    from langchain.chains import RetrievalQA
    from langchain.chains.question_answering import load_qa_chain
except ImportError:
    print("⚠️ LangChain chains not available")
    RetrievalQA = None
    load_qa_chain = None

def test_langchain_llm_response():
    """Test of een eenvoudige LangChain QA-chain werkt."""
    try:
        if OpenAI is None or load_qa_chain is None:
            print("⚠️ LangChain components niet beschikbaar - test overgeslagen")
            return

        llm = OpenAI(temperature=0.0)
        chain = load_qa_chain(llm, chain_type="stuff")
        docs = ["LangChain is een framework om applicaties met LLMs te bouwen."]
        question = "Wat is LangChain?"
        response = chain.run(input_documents=docs, question=question)
        assert "framework" in response.lower()
        print("✅ LangChain test succesvol:", response)
    except Exception as e:
        print("❌ LangChain test gefaald:", str(e))

if __name__ == "__main__":
    test_langchain_llm_response()
