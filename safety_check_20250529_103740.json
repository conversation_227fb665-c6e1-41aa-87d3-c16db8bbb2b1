{"timestamp": "2025-05-29T10:37:40.681692", "all_passed": false, "passed_checks": 3, "total_checks": 8, "results": {"🔐 API Permissions": {"passed": false, "details": ["Exchange manager not available"], "timestamp": "2025-05-29T10:37:40.681477"}, "💰 Balance Verification": {"passed": false, "details": ["Exchange manager not available"], "timestamp": "2025-05-29T10:37:40.681492"}, "⚙️ Configuration Validation": {"passed": false, "details": ["Settings not available"], "timestamp": "2025-05-29T10:37:40.681499"}, "🔗 Exchange Connectivity": {"passed": false, "details": ["Exchange manager not available"], "timestamp": "2025-05-29T10:37:40.681505"}, "🛡️ Risk Management": {"passed": true, "details": ["Stop-loss system: ✅ Configured", "Take-profit system: ✅ Configured", "Position sizing: ✅ Risk-based", "Daily loss limits: ✅ Active", "Emergency stop: ✅ Available"], "timestamp": "2025-05-29T10:37:40.681510"}, "📊 Trading Pairs": {"passed": false, "details": ["Exchange manager not available"], "timestamp": "2025-05-29T10:37:40.681519"}, "🚨 Emergency Controls": {"passed": true, "details": ["Emergency stop button: ✅ Available in bot", "Manual override: ✅ Admin access required", "Position closure: ✅ Can close all positions", "Trading halt: ✅ Can stop all trading"], "timestamp": "2025-05-29T10:37:40.681524"}, "📝 Logging & Monitoring": {"passed": true, "details": ["Trade logging: ✅ All trades logged", "Error logging: ✅ Comprehensive error tracking", "Performance monitoring: ✅ Real-time metrics", "Alert system: ✅ Telegram notifications"], "timestamp": "2025-05-29T10:37:40.681577"}}}