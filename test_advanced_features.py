#!/usr/bin/env python3
"""
Test script voor alle nieuwe advanced features
"""
import asyncio
import sys
from pathlib import Path
from decimal import Decimal

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent))

async def test_paper_trading():
    """Test paper trading functionality"""
    print("\n🧪 Testing Paper Trading...")
    
    try:
        from core.paper_trading import get_paper_trading_manager
        
        paper_manager = get_paper_trading_manager()
        
        # Test initial portfolio
        portfolio = paper_manager.get_portfolio_value()
        print(f"✅ Initial Portfolio: ${portfolio['total_value']:.2f}")
        
        # Test buy order
        order = await paper_manager.create_order("BTC/USDT", "buy", Decimal('0.001'))
        if order:
            print(f"✅ Buy order created: {order.amount} BTC/USDT @ ${order.price}")
        else:
            print("❌ Failed to create buy order")
        
        # Test portfolio after trade
        portfolio = paper_manager.get_portfolio_value()
        print(f"✅ Portfolio after trade: ${portfolio['total_value']:.2f}")
        
        # Test balances
        balances = paper_manager.get_balance()
        for asset, balance in balances.items():
            if balance['total'] > 0:
                print(f"   {asset}: {balance['total']:.6f}")
        
        return True
        
    except Exception as e:
        print(f"❌ Paper trading test failed: {e}")
        return False

async def test_advanced_dashboard():
    """Test advanced dashboard functionality"""
    print("\n🧪 Testing Advanced Dashboard...")
    
    try:
        from core.dashboard import get_dashboard
        
        dashboard = get_dashboard()
        
        # Test advanced metrics (will be empty initially)
        metrics = dashboard.get_advanced_metrics()
        print(f"✅ Advanced metrics: {metrics}")
        
        # Test performance report
        report = dashboard.generate_performance_report()
        print(f"✅ Performance report generated")
        
        return True
        
    except Exception as e:
        print(f"❌ Dashboard test failed: {e}")
        return False

async def test_advanced_analyzer():
    """Test advanced market analyzer"""
    print("\n🧪 Testing Advanced Market Analyzer...")
    
    try:
        from analysis.advanced_analyzer import get_advanced_analyzer
        
        analyzer = get_advanced_analyzer()
        await analyzer.initialize()
        
        # Test market overview
        overview = await analyzer.get_market_overview()
        print(f"✅ Market overview: Total cap ${overview.get('total_market_cap', 0)/1e12:.1f}T")
        
        # Test deep analysis
        analysis = await analyzer.analyze_symbol_deep("BTC/USDT")
        if 'error' not in analysis:
            print(f"✅ BTC/USDT analysis: {analysis['overall_signal']['signal']}")
            print(f"   Technical trend: {analysis['technical_analysis']['trend']}")
            print(f"   Sentiment: {analysis['market_sentiment']['overall_sentiment']}")
        else:
            print(f"❌ Analysis failed: {analysis['error']}")
        
        await analyzer.cleanup()
        return True
        
    except Exception as e:
        print(f"❌ Advanced analyzer test failed: {e}")
        return False

def test_production_manager():
    """Test production manager"""
    print("\n🧪 Testing Production Manager...")
    
    try:
        from core.production_manager import get_production_manager
        
        prod_manager = get_production_manager()
        
        # Test production readiness check
        readiness = prod_manager.check_production_readiness()
        print(f"✅ Production readiness: {readiness['overall_score']:.1f}%")
        print(f"   Production ready: {readiness['production_ready']}")
        
        # Show recommendations
        for rec in readiness['recommendations'][:3]:  # Show first 3
            print(f"   {rec}")
        
        # Test backup creation
        backup_result = prod_manager.create_backup()
        if backup_result['success']:
            print(f"✅ Backup created: {backup_result['backup_name']}")
            print(f"   Size: {backup_result['backup_size_mb']} MB")
        else:
            print(f"❌ Backup failed: {backup_result['error']}")
        
        # Test deployment script generation
        script_path = prod_manager.generate_deployment_script()
        print(f"✅ Deployment script: {script_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ Production manager test failed: {e}")
        return False

async def test_telegram_commands():
    """Test new Telegram commands"""
    print("\n🧪 Testing Telegram Commands...")
    
    try:
        from bot.commands import PaperTradingCommand, PerformanceCommand
        from bot.telegram_handler import TelegramMessage
        
        # Create mock message
        mock_message = TelegramMessage(
            chat_id=123456789,
            user_id=123456789,
            message_id=1,
            text="/paper",
            timestamp=0
        )
        
        # Test paper trading command
        paper_cmd = PaperTradingCommand()
        print(f"✅ Paper trading command: {paper_cmd.name}")
        
        # Test performance command
        perf_cmd = PerformanceCommand()
        print(f"✅ Performance command: {perf_cmd.name}")
        
        return True
        
    except Exception as e:
        print(f"❌ Telegram commands test failed: {e}")
        return False

async def main():
    """Main test function"""
    print("🧪 ADVANCED FEATURES TEST SUITE")
    print("=" * 50)
    
    tests = [
        ("Paper Trading", test_paper_trading()),
        ("Advanced Dashboard", test_advanced_dashboard()),
        ("Advanced Analyzer", test_advanced_analyzer()),
        ("Production Manager", lambda: test_production_manager()),
        ("Telegram Commands", test_telegram_commands())
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔍 Running {test_name} test...")
        try:
            if asyncio.iscoroutine(test_func):
                result = await test_func
            else:
                result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("🎯 TEST RESULTS SUMMARY")
    print("=" * 50)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} - {test_name}")
        if result:
            passed += 1
    
    print(f"\n📊 Overall: {passed}/{len(results)} tests passed ({passed/len(results)*100:.1f}%)")
    
    if passed == len(results):
        print("\n🎉 ALL TESTS PASSED! Advanced features are ready to use.")
        print("\n🚀 You can now use:")
        print("   • /paper - Paper trading dashboard")
        print("   • /performance - Advanced performance metrics")
        print("   • Advanced market analysis")
        print("   • Production deployment tools")
    else:
        print(f"\n⚠️ {len(results) - passed} tests failed. Check the errors above.")
    
    return passed == len(results)

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n🛑 Tests interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Test suite failed: {e}")
        sys.exit(1)
