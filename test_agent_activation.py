#!/usr/bin/env python3
"""
Test script voor agent activatie en risicobeheer
"""
import asyncio
import sys
import os
from decimal import Decimal

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from exchanges.manager import ExchangeManager
from strategies.manager import StrategyManager
from core.risk_manager import AdvancedRiskManager
from core.agent_controller import TradingAgentController, AgentStatus
from loguru import logger

async def test_risk_manager():
    """Test risk manager functionality"""
    logger.info("🧪 Testing Risk Manager...")
    
    # Initialize components
    exchange_manager = ExchangeManager()
    risk_manager = AdvancedRiskManager(exchange_manager)
    
    # Test initialization
    await risk_manager._initialize_daily_tracking()
    logger.info("✅ Risk manager initialized")
    
    # Test risk metrics
    metrics = await risk_manager.get_risk_metrics()
    logger.info(f"📊 Risk metrics: Balance=${metrics.total_balance:.2f}, Risk={metrics.risk_percentage:.1f}%")
    
    # Test trade validation
    valid, reason = await risk_manager.validate_new_trade(
        "BTC/USDT", "buy", Decimal('0.001'), Decimal('95000'), Decimal('93000')
    )
    logger.info(f"🔍 Trade validation: {valid} - {reason}")
    
    # Test risk summary
    summary = await risk_manager.get_risk_summary()
    logger.info(f"📋 Risk summary:\n{summary}")
    
    return risk_manager

async def test_agent_controller():
    """Test agent controller functionality"""
    logger.info("🤖 Testing Agent Controller...")
    
    # Initialize components
    exchange_manager = ExchangeManager()
    risk_manager = AdvancedRiskManager(exchange_manager)
    strategy_manager = StrategyManager(exchange_manager, risk_manager)
    
    # Initialize risk tracking
    await risk_manager._initialize_daily_tracking()
    
    # Create agent controller
    agent_controller = TradingAgentController(
        exchange_manager, strategy_manager, risk_manager
    )
    logger.info("✅ Agent controller created")
    
    # Test agent metrics
    metrics = await agent_controller.get_agent_metrics()
    logger.info(f"📊 Agent metrics: Status={metrics.status.value}, Confidence={metrics.confidence_level:.1f}%")
    
    # Test agent activation
    logger.info("🚀 Testing agent activation...")
    success, message = await agent_controller.activate_agent()
    logger.info(f"🔄 Activation result: {success} - {message}")
    
    if success:
        # Test status summary
        status_summary = await agent_controller.get_status_summary()
        logger.info(f"📋 Agent status:\n{status_summary}")
        
        # Test deactivation
        logger.info("🛑 Testing agent deactivation...")
        success, message = await agent_controller.deactivate_agent()
        logger.info(f"🔄 Deactivation result: {success} - {message}")
    
    return agent_controller

async def test_integration():
    """Test full integration"""
    logger.info("🔗 Testing Full Integration...")
    
    try:
        # Initialize all components
        exchange_manager = ExchangeManager()
        risk_manager = AdvancedRiskManager(exchange_manager)
        strategy_manager = StrategyManager(exchange_manager, risk_manager)
        agent_controller = TradingAgentController(
            exchange_manager, strategy_manager, risk_manager
        )
        
        # Initialize risk tracking
        await risk_manager._initialize_daily_tracking()
        
        # Connect to exchanges
        connection_results = await exchange_manager.connect_all()
        connected_exchanges = [name for name, connected in connection_results.items() if connected]
        logger.info(f"🔗 Connected exchanges: {connected_exchanges}")
        
        # Test risk validation
        logger.info("🛡️ Testing risk validation...")
        valid, reason = await risk_manager.validate_new_trade(
            "BTC/USDT", "buy", Decimal('0.001'), Decimal('95000'), Decimal('93000')
        )
        logger.info(f"✅ Risk validation: {valid} - {reason}")
        
        # Test agent activation with real conditions
        logger.info("🤖 Testing agent activation with real conditions...")
        success, message = await agent_controller.activate_agent()
        logger.info(f"🚀 Agent activation: {success} - {message}")
        
        if success:
            # Get comprehensive status
            status_summary = await agent_controller.get_status_summary()
            risk_summary = await risk_manager.get_risk_summary()
            
            logger.info("📊 COMPREHENSIVE STATUS:")
            logger.info(f"\n{status_summary}")
            logger.info(f"\n{risk_summary}")
            
            # Test emergency stop
            logger.info("🚨 Testing emergency stop...")
            success, message = await agent_controller.emergency_stop("Test emergency stop")
            logger.info(f"🛑 Emergency stop: {success} - {message}")
            
            # Test reset
            logger.info("🔧 Testing emergency reset...")
            agent_controller.reset_emergency_stop()
            logger.info("✅ Emergency stop reset")
        
        logger.info("✅ Integration test completed successfully!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Integration test failed: {e}")
        return False

async def test_position_tracking():
    """Test position tracking with risk manager"""
    logger.info("📊 Testing Position Tracking...")
    
    try:
        # Initialize components
        exchange_manager = ExchangeManager()
        risk_manager = AdvancedRiskManager(exchange_manager)
        await risk_manager._initialize_daily_tracking()
        
        # Test adding positions
        logger.info("➕ Testing position addition...")
        await risk_manager.add_position(
            "BTC/USDT", "buy", Decimal('0.001'), Decimal('95000'), Decimal('93000')
        )
        
        await risk_manager.add_position(
            "ETH/USDT", "buy", Decimal('0.01'), Decimal('3500'), Decimal('3400')
        )
        
        # Test position updates
        logger.info("🔄 Testing position updates...")
        await risk_manager.update_position("BTC/USDT", Decimal('96000'))
        await risk_manager.update_position("ETH/USDT", Decimal('3450'))
        
        # Get updated metrics
        metrics = await risk_manager.get_risk_metrics()
        logger.info(f"📊 Updated metrics: Positions={metrics.open_positions}, P&L=${metrics.unrealized_pnl:.2f}")
        
        # Test position removal
        logger.info("➖ Testing position removal...")
        pnl = await risk_manager.remove_position("BTC/USDT", "buy")
        logger.info(f"💰 Realized P&L: ${pnl:.2f}")
        
        # Final metrics
        final_metrics = await risk_manager.get_risk_metrics()
        logger.info(f"📊 Final metrics: Positions={final_metrics.open_positions}")
        
        logger.info("✅ Position tracking test completed!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Position tracking test failed: {e}")
        return False

async def main():
    """Main test function"""
    logger.info("🧪 Starting Agent Activation and Risk Management Tests")
    logger.info("=" * 60)
    
    try:
        # Test individual components
        logger.info("1️⃣ Testing Risk Manager...")
        risk_manager = await test_risk_manager()
        logger.info("✅ Risk Manager test completed\n")
        
        logger.info("2️⃣ Testing Agent Controller...")
        agent_controller = await test_agent_controller()
        logger.info("✅ Agent Controller test completed\n")
        
        logger.info("3️⃣ Testing Position Tracking...")
        await test_position_tracking()
        logger.info("✅ Position Tracking test completed\n")
        
        logger.info("4️⃣ Testing Full Integration...")
        integration_success = await test_integration()
        logger.info("✅ Integration test completed\n")
        
        # Summary
        logger.info("=" * 60)
        logger.info("🎉 ALL TESTS COMPLETED!")
        logger.info(f"📊 Integration Success: {integration_success}")
        logger.info("🚀 Agent activation and risk management system is ready!")
        
    except Exception as e:
        logger.error(f"❌ Test suite failed: {e}")
        return False
    
    return True

if __name__ == "__main__":
    # Configure logging
    logger.remove()
    logger.add(
        sys.stdout,
        format="<green>{time:HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{message}</cyan>",
        level="INFO"
    )
    
    # Run tests
    asyncio.run(main())
