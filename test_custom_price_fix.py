#!/usr/bin/env python3
"""
Test script om de custom price fix te testen
"""
import asyncio
import sys
from pathlib import Path

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent))

async def test_custom_price_callback():
    """Test de custom price callback fix"""
    print("🧪 Testing Custom Price Callback Fix...")
    
    try:
        # Import the bot class
        from telegram_simple import SimpleTelegramBot
        
        # Create a mock bot instance
        bot = SimpleTelegramBot()
        
        # Test the callback handler directly
        chat_id = 123456789
        message_id = 1
        
        print("✅ Testing price_custom callback...")
        
        # This should NOT try to fetch ticker for "custom" anymore
        # Instead it should show the custom input message
        await bot._handle_price_callback(chat_id, message_id, "price_custom")
        
        print("✅ Custom price callback test completed without errors!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

async def test_custom_price_input():
    """Test de custom price input handler"""
    print("\n🧪 Testing Custom Price Input Handler...")
    
    try:
        from telegram_simple import SimpleTelegramBot
        
        bot = SimpleTelegramBot()
        
        # Test valid input
        print("✅ Testing valid symbol input...")
        await bot._handle_custom_price_input(123456789, 123456789, "DOGE/USDT", 1)
        
        # Test invalid input
        print("✅ Testing invalid symbol input...")
        await bot._handle_custom_price_input(123456789, 123456789, "INVALID", 1)
        
        print("✅ Custom price input test completed!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

async def main():
    """Main test function"""
    print("🔧 CUSTOM PRICE FIX TEST")
    print("=" * 40)
    
    tests = [
        ("Custom Price Callback", test_custom_price_callback()),
        ("Custom Price Input", test_custom_price_input())
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔍 Running {test_name} test...")
        try:
            result = await test_func
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 40)
    print("🎯 TEST RESULTS")
    print("=" * 40)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} - {test_name}")
        if result:
            passed += 1
    
    print(f"\n📊 Overall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("\n🎉 ALL TESTS PASSED!")
        print("✅ Custom price error is FIXED!")
        print("\n📱 Now when users click '🔍 Andere' they will:")
        print("   • See a custom input message")
        print("   • Be able to type any symbol")
        print("   • Get proper error handling")
        print("   • NO MORE 'custom' symbol errors!")
    else:
        print(f"\n⚠️ {len(results) - passed} tests failed.")
    
    return passed == len(results)

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n🛑 Tests interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Test suite failed: {e}")
        sys.exit(1)
