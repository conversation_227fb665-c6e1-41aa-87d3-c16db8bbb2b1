#!/usr/bin/env python3
"""
Test script voor Heartbeat Monitor
"""
import asyncio
import sys
from pathlib import Path

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent))

from heartbeat_monitor import HeartbeatMonitor
from bot.notify import TelegramNotifier
from config.settings import Settings

async def test_notifier():
    """Test de TelegramNotifier"""
    print("🧪 Testing TelegramNotifier...")
    
    try:
        settings = Settings()
        notifier = TelegramNotifier(settings)
        
        # Initialize
        if await notifier.initialize():
            print("✅ Notifier initialized successfully")
            
            # Test basic message
            success = await notifier.send_to_admins("🧪 Test bericht van Heartbeat Monitor")
            if success:
                print("✅ Test message sent successfully")
            else:
                print("❌ Failed to send test message")
                
            # Test heartbeat
            test_data = {
                'uptime': '0:01:30',
                'status': '🟢 Test Mode',
                'strategies': ['Test Strategy'],
                'last_trade': {
                    'symbol': 'BTC/USDT',
                    'side': 'buy',
                    'timestamp': '12:34:56'
                },
                'trade_count': 1
            }
            
            success = await notifier.send_heartbeat(test_data)
            if success:
                print("✅ Test heartbeat sent successfully")
            else:
                print("❌ Failed to send test heartbeat")
                
            # Test alert
            success = await notifier.send_alert(
                alert_type="system",
                message="Test alert van Heartbeat Monitor",
                priority="normal"
            )
            if success:
                print("✅ Test alert sent successfully")
            else:
                print("❌ Failed to send test alert")
                
            await notifier.cleanup()
            
        else:
            print("❌ Failed to initialize notifier")
            
    except Exception as e:
        print(f"❌ Error testing notifier: {e}")

async def test_monitor():
    """Test de HeartbeatMonitor"""
    print("\n🧪 Testing HeartbeatMonitor...")
    
    try:
        monitor = HeartbeatMonitor()
        
        # Test initialization
        if await monitor.initialize():
            print("✅ Monitor initialized successfully")
            
            # Test process finding
            await monitor._find_main_process()
            if monitor.main_process:
                print(f"✅ Found process: PID {monitor.main_process.pid}")
            else:
                print("⚠️ No main.py process found (this is normal for testing)")
                
            # Test status check
            status = await monitor._check_main_process_status()
            print(f"📊 Process status: {status}")
            
            # Test strategy status
            strategies = await monitor._get_strategy_status()
            print(f"🎯 Strategies: {strategies}")
            
            # Test trade info
            trade_info = await monitor._get_trade_info()
            print(f"💰 Trade info: {trade_info}")
            
            # Test system metrics
            metrics = await monitor._collect_system_metrics()
            print(f"💻 System metrics: CPU {metrics.get('cpu_usage', 0):.1f}%, Memory {metrics.get('memory_usage', 0):.1f}%")
            
            # Test single heartbeat (don't start full loop)
            print("\n💓 Sending test heartbeat...")
            await monitor._send_heartbeat()
            
            await monitor.cleanup()
            print("✅ Monitor test completed")
            
        else:
            print("❌ Failed to initialize monitor")
            
    except Exception as e:
        print(f"❌ Error testing monitor: {e}")

async def test_configuration():
    """Test configuratie"""
    print("\n🧪 Testing Configuration...")
    
    try:
        settings = Settings()
        
        # Check Telegram token
        if settings.telegram_bot_token:
            print("✅ Telegram bot token configured")
        else:
            print("❌ Telegram bot token missing")
            
        # Check admin users
        if settings.telegram_admin_user_ids:
            print(f"✅ Admin users configured: {len(settings.telegram_admin_user_ids)} users")
        else:
            print("❌ No admin users configured")
            
        # Check other settings
        print(f"📊 Exchange: {settings.exchange}")
        print(f"🔧 Live mode: {settings.live_mode}")
        print(f"🧪 Use testnet: {settings.use_testnet}")
        
    except Exception as e:
        print(f"❌ Error testing configuration: {e}")

async def main():
    """Main test function"""
    print("🧪 HEARTBEAT MONITOR TEST SUITE")
    print("=" * 50)
    
    # Test configuration first
    await test_configuration()
    
    # Test notifier
    await test_notifier()
    
    # Test monitor
    await test_monitor()
    
    print("\n" + "=" * 50)
    print("🎉 Test suite completed!")
    print("\nℹ️  Als alle tests slagen, kun je de monitor starten met:")
    print("   python3 heartbeat_monitor.py")
    print("   of")
    print("   ./start_heartbeat.sh")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n🛑 Test interrupted by user")
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        sys.exit(1)
